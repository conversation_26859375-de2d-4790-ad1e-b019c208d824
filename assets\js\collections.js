// Collections Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initCollectionFilters();
    initCollectionLightbox();
    initCollectionAnimations();
    initCollectionLinks();
    equalizeBoxHeights();
});

// Image to item ID mapping
const imageToItemMap = {
    '1486d96b-d890-493e-bfbe-36f3ebb739e2.jpeg': 'div001',
    '1a6213a9-eee5-4f66-8918-b92456b280d2.jpeg': 'div002',
    '1f917925-364b-4087-a626-d3bc4fdadd3e.jpeg': 'div003',
    '25a559be-aa7b-4d3a-981c-e3576b0c21ef.jpeg': 'div001',
    '2b7572ff-d063-45d9-af74-b941bd083d81.jpeg': 'div002',
    '37c7d161-a2ad-462f-a0f4-4021fd09bd52.jpeg': 'div006',
    '302026b0-f188-4cf9-a5e0-a86363a91327.jpeg': 'art001',
    '77ba796f-27e2-487f-a9b4-07c149921578.jpeg': 'art002',
    '90533560-6d2a-44be-87a2-61ff3fad1ab0.jpeg': 'art003',
    '90a66135-4eda-4569-b2a9-8f45b5b158ef.jpeg': 'art004',
    '81303df7-89d1-4279-95bf-5cbb133f5578.jpeg': 'stat001',
    'b969c1f1-4ad1-48dc-95d1-30545d3df744.jpeg': 'stat002',
    'temple-1.jpeg': 'temp001',
    'temple-2.jpeg': 'temp002',
    'temple-3.jpeg': 'temp003',
    'temple-4.jpeg': 'temp004'
};

// Collection filtering functionality
function initCollectionFilters() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    const collectionItems = document.querySelectorAll('.collection-item');
    const collectionSections = document.querySelectorAll('.collection-section');
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Filter collections
            if (filter === 'all') {
                // Show all sections
                collectionSections.forEach(section => {
                    section.style.display = 'block';
                    animateItems(section.querySelectorAll('.collection-item'));
                });
            } else {
                // Hide all sections first
                collectionSections.forEach(section => {
                    section.style.display = 'none';
                });
                
                // Show specific section
                const targetSection = document.getElementById(filter);
                if (targetSection) {
                    targetSection.style.display = 'block';
                    animateItems(targetSection.querySelectorAll('.collection-item'));
                    
                    // Smooth scroll to section
                    setTimeout(() => {
                        targetSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }, 100);
                }
            }
        });
    });
}

// Animate collection items
function animateItems(items) {
    items.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Lightbox functionality for collection items
function initCollectionLightbox() {
    const viewBtns = document.querySelectorAll('.view-btn');
    
    viewBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            
            const item = this.closest('.collection-item');
            const img = item.querySelector('img');
            const title = item.querySelector('h3').textContent;
            const description = item.querySelector('p').textContent;
            
            openCollectionLightbox(img.src, img.alt, title, description);
        });
    });
    
    // Also allow clicking on the item itself
    const collectionItems = document.querySelectorAll('.collection-item');
    collectionItems.forEach(item => {
        item.addEventListener('click', function() {
            const img = this.querySelector('img');
            const title = this.querySelector('h3').textContent;
            const description = this.querySelector('p').textContent;
            
            openCollectionLightbox(img.src, img.alt, title, description);
        });
    });
}

// Open lightbox with collection item details
function openCollectionLightbox(src, alt, title, description) {
    // Create lightbox
    const lightbox = document.createElement('div');
    lightbox.className = 'lightbox';
    lightbox.innerHTML = `
        <div class="lightbox-content">
            <img src="${src}" alt="${alt}" class="lightbox-image">
            <div class="lightbox-info">
                <h3>${title}</h3>
                <p>${description}</p>
            </div>
            <button class="lightbox-close">&times;</button>
        </div>
    `;
    
    document.body.appendChild(lightbox);
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
    
    // Animate in
    setTimeout(() => {
        lightbox.classList.add('active');
    }, 100);
    
    // Close functionality
    const closeBtn = lightbox.querySelector('.lightbox-close');
    closeBtn.addEventListener('click', closeLightbox);
    
    lightbox.addEventListener('click', function(e) {
        if (e.target === lightbox) {
            closeLightbox();
        }
    });
    
    // Close on escape key
    const escapeHandler = function(e) {
        if (e.key === 'Escape') {
            closeLightbox();
        }
    };
    document.addEventListener('keydown', escapeHandler);
    
    function closeLightbox() {
        lightbox.classList.remove('active');
        document.body.style.overflow = '';
        document.removeEventListener('keydown', escapeHandler);
        
        setTimeout(() => {
            if (document.body.contains(lightbox)) {
                document.body.removeChild(lightbox);
            }
        }, 300);
    }
}

// Collection animations on scroll
function initCollectionAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe collection items
    const collectionItems = document.querySelectorAll('.collection-item');
    collectionItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        item.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
        observer.observe(item);
    });
    
    // Observe section headers
    const sectionHeaders = document.querySelectorAll('.section-header');
    sectionHeaders.forEach(header => {
        header.style.opacity = '0';
        header.style.transform = 'translateY(30px)';
        header.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(header);
    });
}

// Search functionality (if search box is added)
function initCollectionSearch() {
    const searchInput = document.querySelector('.search-box input');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const collectionItems = document.querySelectorAll('.collection-item');
            
            collectionItems.forEach(item => {
                const title = item.querySelector('h3').textContent.toLowerCase();
                const description = item.querySelector('p').textContent.toLowerCase();
                
                if (title.includes(searchTerm) || description.includes(searchTerm)) {
                    item.style.display = 'block';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }
}

// Sort functionality (if sort dropdown is added)
function initCollectionSort() {
    const sortSelect = document.querySelector('.sort-dropdown select');
    
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            const sortBy = this.value;
            const collectionGrids = document.querySelectorAll('.collection-grid');
            
            collectionGrids.forEach(grid => {
                const items = Array.from(grid.querySelectorAll('.collection-item'));
                
                items.sort((a, b) => {
                    const titleA = a.querySelector('h3').textContent;
                    const titleB = b.querySelector('h3').textContent;
                    
                    switch (sortBy) {
                        case 'name-asc':
                            return titleA.localeCompare(titleB);
                        case 'name-desc':
                            return titleB.localeCompare(titleA);
                        default:
                            return 0;
                    }
                });
                
                // Re-append sorted items
                items.forEach(item => grid.appendChild(item));
                
                // Re-animate items
                animateItems(items);
            });
        });
    }
}

// Lazy loading for images
function initLazyLoading() {
    const images = document.querySelectorAll('.collection-item img');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src || img.src;
                img.classList.remove('lazy');
                observer.unobserve(img);
            }
        });
    });
    
    images.forEach(img => {
        imageObserver.observe(img);
    });
}

// Masonry layout for collection grid (optional)
function initMasonryLayout() {
    const grids = document.querySelectorAll('.collection-grid');
    
    grids.forEach(grid => {
        // Simple masonry-like effect using CSS Grid
        const items = grid.querySelectorAll('.collection-item');
        
        items.forEach(item => {
            const img = item.querySelector('img');
            img.addEventListener('load', function() {
                // Adjust grid row span based on image aspect ratio
                const aspectRatio = this.naturalHeight / this.naturalWidth;
                const rowSpan = Math.ceil(aspectRatio * 10);
                item.style.gridRowEnd = `span ${Math.min(rowSpan, 20)}`;
            });
        });
    });
}

// Collection statistics
function updateCollectionStats() {
    const divinityCount = document.querySelectorAll('#divinity .collection-item').length;
    const artifactsCount = document.querySelectorAll('#artifacts .collection-item').length;
    const statuesCount = document.querySelectorAll('#statues .collection-item').length;
    const templesCount = document.querySelectorAll('#temples .collection-item').length;
    const totalCount = divinityCount + artifactsCount + statuesCount + templesCount;

    // Update filter button text with counts
    const filterBtns = document.querySelectorAll('.filter-btn');
    filterBtns.forEach(btn => {
        const filter = btn.getAttribute('data-filter');
        const currentText = btn.textContent;

        switch (filter) {
            case 'all':
                btn.textContent = `All Collections (${totalCount})`;
                break;
            case 'divinity':
                btn.textContent = `Divinity (${divinityCount})`;
                break;
            case 'artifacts':
                btn.textContent = `Artifacts (${artifactsCount})`;
                break;
            case 'statues':
                btn.textContent = `Statues (${statuesCount})`;
                break;
            case 'temples':
                btn.textContent = `Temples (${templesCount})`;
                break;
        }
    });
}

// Initialize additional features when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initCollectionSearch();
    initCollectionSort();
    initLazyLoading();
    updateCollectionStats();
    
    // Add smooth scrolling to filter navigation
    const filterBtns = document.querySelectorAll('.filter-btn');
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            if (filter !== 'all') {
                setTimeout(() => {
                    const targetSection = document.getElementById(filter);
                    if (targetSection) {
                        const offsetTop = targetSection.offsetTop - 150;
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                }, 300);
            }
        });
    });
});

// Preload images for better performance
function preloadImages() {
    const images = document.querySelectorAll('.collection-item img');
    images.forEach(img => {
        const imagePreload = new Image();
        imagePreload.src = img.src;
    });
}

// Call preload after page load
window.addEventListener('load', preloadImages);

function initCollectionLinks() {
    const collectionItems = document.querySelectorAll('.collection-item');

    collectionItems.forEach(item => {
        const img = item.querySelector('img');
        const viewBtn = item.querySelector('.view-btn');

        if (img) {
            const imageSrc = img.src;
            let itemId = null;

            // Find matching item ID
            for (const [imageFile, id] of Object.entries(imageToItemMap)) {
                if (imageSrc.includes(imageFile)) {
                    itemId = id;
                    break;
                }
            }

            if (itemId) {
                // Make entire item clickable
                item.style.cursor = 'pointer';
                item.addEventListener('click', function(e) {
                    if (!e.target.closest('.view-btn')) {
                        window.location.href = `item-${itemId}.html`;
                    }
                });

                // Update view button to be a link
                if (viewBtn) {
                    viewBtn.onclick = function(e) {
                        e.stopPropagation();
                        window.location.href = `item-${itemId}.html`;
                    };
                }

                // Add hover effects
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                    this.style.boxShadow = '0 20px 40px rgba(0,0,0,0.15)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 8px 32px rgba(0,0,0,0.1)';
                });
            }
        }
    });
}

function equalizeBoxHeights() {
    // Equalize heights within each collection section
    const collectionSections = document.querySelectorAll('.collection-section');

    collectionSections.forEach(section => {
        const items = section.querySelectorAll('.collection-item');
        const itemInfos = section.querySelectorAll('.item-info');

        // Reset heights
        items.forEach(item => {
            item.style.height = 'auto';
        });
        itemInfos.forEach(info => {
            info.style.height = 'auto';
        });

        // Find max heights
        let maxItemHeight = 0;
        let maxInfoHeight = 0;

        items.forEach(item => {
            maxItemHeight = Math.max(maxItemHeight, item.offsetHeight);
        });

        itemInfos.forEach(info => {
            maxInfoHeight = Math.max(maxInfoHeight, info.offsetHeight);
        });

        // Apply max heights
        items.forEach(item => {
            item.style.height = maxItemHeight + 'px';
            item.style.display = 'flex';
            item.style.flexDirection = 'column';
        });

        itemInfos.forEach(info => {
            info.style.height = maxInfoHeight + 'px';
            info.style.display = 'flex';
            info.style.flexDirection = 'column';
            info.style.justifyContent = 'space-between';
        });
    });
}

// Re-equalize heights on window resize
window.addEventListener('resize', function() {
    setTimeout(equalizeBoxHeights, 100);
});
