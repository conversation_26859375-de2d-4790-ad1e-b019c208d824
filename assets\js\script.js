// <PERSON>ha <PERSON> Museum Website JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initHeroCarousel();
    initAdvancedScrollEffects();
    initSmoothScroll();
    initContactForm();
    initGallery();
    initAnimations();
    initItemShowcase();
    initFeaturedCarousel();
});

// Initialize item showcase on homepage
function initItemShowcase() {
    const showcaseGrid = document.getElementById('showcaseGrid');

    console.log('Initializing showcase...');
    console.log('Showcase grid found:', !!showcaseGrid);
    console.log('getAllItems function available:', typeof getAllItems === 'function');

    if (showcaseGrid) {
        if (typeof getAllItems === 'function') {
            try {
                const allItems = getAllItems();
                console.log('All items loaded:', allItems.length);
                if (allItems.length > 0) {
                    const randomItems = allItems.sort(() => 0.5 - Math.random()).slice(0, 8);
                    console.log('Random items selected:', randomItems.length);

        showcaseGrid.innerHTML = randomItems.map(item => `
            <div class="showcase-item" data-item-id="${item.id}">
                <div class="showcase-image">
                    <img src="${item.images[0]}" alt="${item.name}" loading="lazy">
                    <div class="showcase-overlay">
                        <div class="showcase-overlay-content">
                            <span class="showcase-overlay-category">${item.category}</span>
                            <h4>${item.name}</h4>
                            <p>${item.period}</p>
                        </div>
                    </div>
                </div>
                <div class="showcase-content">
                    <span class="showcase-category">${item.category}</span>
                    <h3>${item.name}</h3>
                    <div class="showcase-meta">${item.period} • ${item.material}</div>
                    <p class="showcase-description">${item.description.substring(0, 120)}...</p>
                    <a href="item-${item.id}.html" class="showcase-link">View Details</a>
                </div>
            </div>
        `).join('');

        // Add click functionality and animation classes
        const showcaseItems = showcaseGrid.querySelectorAll('.showcase-item');
        showcaseItems.forEach(item => {
            item.classList.add('animate-element');

            // Add click functionality to entire item
            item.style.cursor = 'pointer';
            item.addEventListener('click', function(e) {
                // Don't trigger if clicking on the link directly
                if (!e.target.classList.contains('showcase-link')) {
                    const itemId = this.getAttribute('data-item-id');
                    window.location.href = `item-${itemId}.html`;
                }
            });

            // Add hover effects
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
                } else {
                    console.log('No items found in database, loading static items...');
                    loadStaticShowcaseItems(showcaseGrid);
                }
            } catch (error) {
                console.log('Error loading items from database:', error);
                loadStaticShowcaseItems(showcaseGrid);
            }
        } else {
            console.log('getAllItems function not available, loading static items...');
            loadStaticShowcaseItems(showcaseGrid);
        }
    } else {
        console.log('Showcase grid not found!');
    }
}

// Fallback function to load static showcase items
function loadStaticShowcaseItems(showcaseGrid) {
    const staticItems = [
        {
            id: 'div001',
            category: 'Divinity',
            name: 'Sacred Deity Sculpture',
            period: '12th-13th Century',
            material: 'Carved Stone',
            images: ['Data/Product Photos/Divinity/25a559be-aa7b-4d3a-981c-e3576b0c21ef.jpeg']
        },
        {
            id: 'div002',
            category: 'Divinity',
            name: 'Spiritual Guardian',
            period: '11th-12th Century',
            material: 'Marble',
            images: ['Data/Product Photos/Divinity/2b7572ff-d063-45d9-af74-b941bd083d81.jpeg']
        },
        {
            id: 'art001',
            category: 'Artifacts',
            name: 'Ceremonial Vessel',
            period: '10th-11th Century',
            material: 'Copper Alloy',
            images: ['Data/Product Photos/Artefacts/302026b0-f188-4cf9-a5e0-a86363a91327.jpeg']
        },
        {
            id: 'art002',
            category: 'Artifacts',
            name: 'Ancient Relic',
            period: '9th-10th Century',
            material: 'Bronze',
            images: ['Data/Product Photos/Artefacts/77ba796f-27e2-487f-a9b4-07c149921578.jpeg']
        },
        {
            id: 'stat001',
            category: 'Statues',
            name: 'Majestic Statue',
            period: '11th-12th Century',
            material: 'Granite',
            images: ['Data/Product Photos/Statues/81303df7-89d1-4279-95bf-5cbb133f5578.jpeg']
        },
        {
            id: 'stat002',
            category: 'Statues',
            name: 'Sacred Monument',
            period: '10th-11th Century',
            material: 'Sandstone',
            images: ['Data/Product Photos/Statues/b969c1f1-4ad1-48dc-95d1-30545d3df744.jpeg']
        },
        {
            id: 'temp001',
            category: 'Temples',
            name: 'Grand Temple Design',
            period: 'Contemporary',
            material: 'Makrana Marble',
            images: ['Data/Product Photos/Temples/temple-1.jpeg']
        },
        {
            id: 'div003',
            category: 'Divinity',
            name: 'Divine Sculpture',
            period: '12th Century',
            material: 'White Marble',
            images: ['Data/Product Photos/Divinity/1486d96b-d890-493e-bfbe-36f3ebb739e2.jpeg']
        }
    ];

    showcaseGrid.innerHTML = staticItems.map(item => `
        <div class="showcase-item" data-item-id="${item.id}">
            <div class="showcase-image">
                <img src="${item.images[0]}" alt="${item.name}" loading="lazy">
                <div class="showcase-overlay">
                    <div class="showcase-overlay-content">
                        <span class="showcase-overlay-category">${item.category}</span>
                        <h4>${item.name}</h4>
                        <p>${item.period}</p>
                    </div>
                </div>
            </div>
            <div class="showcase-content">
                <span class="showcase-category">${item.category}</span>
                <h3>${item.name}</h3>
                <div class="showcase-meta">${item.period} • ${item.material}</div>
                <a href="item-${item.id}.html" class="showcase-link">View Details</a>
            </div>
        </div>
    `).join('');

    // Add click functionality
    const showcaseItems = showcaseGrid.querySelectorAll('.showcase-item');
    showcaseItems.forEach(item => {
        item.style.cursor = 'pointer';
        item.addEventListener('click', function(e) {
            if (!e.target.classList.contains('showcase-link')) {
                const itemId = this.getAttribute('data-item-id');
                window.location.href = `item-${itemId}.html`;
            }
        });
    });
}

// Initialize CSS animations
function initAnimations() {
    // Add CSS for animations
    const style = document.createElement('style');
    style.textContent = `
        .animate-element {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .animate-element.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .animate-child {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .animate-child.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .hero-carousel-left .carousel-track,
        .hero-carousel-right .carousel-track {
            will-change: transform;
        }

        @media (prefers-reduced-motion: reduce) {
            .animate-element,
            .animate-child,
            .carousel-track {
                animation: none !important;
                transition: none !important;
            }
        }
    `;
    document.head.appendChild(style);
}

// Navigation functionality
function initNavigation() {
    const navbar = document.querySelector('.navbar');
    const navLinks = document.querySelectorAll('.nav-link');
    
    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href.startsWith('#')) {
                e.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    const offsetTop = target.offsetTop - 120;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            }
        });
    });
    
    // Active navigation highlighting
    window.addEventListener('scroll', function() {
        const scrollPos = window.scrollY + 150;
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href.startsWith('#')) {
                const section = document.querySelector(href);
                if (section) {
                    const sectionTop = section.offsetTop;
                    const sectionBottom = sectionTop + section.offsetHeight;
                    
                    if (scrollPos >= sectionTop && scrollPos < sectionBottom) {
                        navLinks.forEach(l => l.classList.remove('active'));
                        link.classList.add('active');
                    }
                }
            }
        });
        
        // Header background on scroll
        if (window.scrollY > 100) {
            navbar.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
            navbar.style.backdropFilter = 'blur(10px)';
        } else {
            navbar.style.backgroundColor = '#ffffff';
            navbar.style.backdropFilter = 'none';
        }
    });
}

// Hero carousel functionality
function initHeroCarousel() {
    const carouselTracks = document.querySelectorAll('.carousel-track');

    if (carouselTracks.length === 0) {
        console.log('No carousel tracks found');
        return;
    }

    carouselTracks.forEach((track, index) => {
        const items = track.querySelectorAll('.carousel-item');
        const direction = track.getAttribute('data-direction');

        console.log(`Initializing carousel ${index + 1}, direction: ${direction}, items: ${items.length}`);

        // Clone items multiple times for seamless loop
        for (let i = 0; i < 4; i++) {
            items.forEach(item => {
                const clone = item.cloneNode(true);
                addCarouselItemClick(clone);
                track.appendChild(clone);
            });
        }

        // Add click functionality to original items
        items.forEach(item => addCarouselItemClick(item));

        // Clear any existing animation
        track.style.animation = 'none';
        track.offsetHeight; // Force reflow

        // Apply animation with immediate start
        setTimeout(() => {
            if (direction === 'up') {
                track.style.animation = 'scrollUp 25s linear infinite';
                console.log('Applied scrollUp animation');
            } else if (direction === 'down') {
                track.style.animation = 'scrollDown 25s linear infinite';
                console.log('Applied scrollDown animation');
            }

            // Ensure it's running
            track.style.animationPlayState = 'running';
        }, 50);

        // Add hover pause functionality
        track.addEventListener('mouseenter', function() {
            this.style.animationPlayState = 'paused';
        });

        track.addEventListener('mouseleave', function() {
            this.style.animationPlayState = 'running';
        });
    });
}

// Add click functionality to carousel items
function addCarouselItemClick(item) {
    const img = item.querySelector('img');
    if (img) {
        const imageSrc = img.src;
        const imageName = img.alt;

        // Map image sources to item IDs (you can expand this mapping)
        const imageToItemMap = {
            '1486d96b-d890-493e-bfbe-36f3ebb739e2.jpeg': 'div001',
            '302026b0-f188-4cf9-a5e0-a86363a91327.jpeg': 'art001',
            '25a559be-aa7b-4d3a-981c-e3576b0c21ef.jpeg': 'div001',
            '81303df7-89d1-4279-95bf-5cbb133f5578.jpeg': 'stat001',
            '2b7572ff-d063-45d9-af74-b941bd083d81.jpeg': 'div002',
            '77ba796f-27e2-487f-a9b4-07c149921578.jpeg': 'art001',
            '1a6213a9-eee5-4f66-8918-b92456b280d2.jpeg': 'div002',
            '90533560-6d2a-44be-87a2-61ff3fad1ab0.jpeg': 'art003',
            '1f917925-364b-4087-a626-d3bc4fdadd3e.jpeg': 'div003',
            'b969c1f1-4ad1-48dc-95d1-30545d3df744.jpeg': 'stat002',
            '30abf74a-5582-4a5d-8292-85c4b517369c.jpeg': 'div005',
            '90a66135-4eda-4569-b2a9-8f45b5b158ef.jpeg': 'art004'
        };

        // Find matching item ID
        let itemId = null;
        for (const [imageFile, id] of Object.entries(imageToItemMap)) {
            if (imageSrc.includes(imageFile)) {
                itemId = id;
                break;
            }
        }

        if (itemId) {
            item.style.cursor = 'pointer';
            item.addEventListener('click', function() {
                window.location.href = `item-${itemId}.html`;
            });

            // Add tooltip
            item.title = `Click to view ${imageName}`;
        }
    }
}

// Enhanced scroll effects with intersection observer
function initAdvancedScrollEffects() {
    // Create intersection observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');

                // Stagger animation for child elements
                const children = entry.target.querySelectorAll('.animate-child');
                children.forEach((child, index) => {
                    setTimeout(() => {
                        child.classList.add('animate-in');
                    }, index * 100);
                });
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.section-header, .collection-card, .featured-item, .stat-item');
    animateElements.forEach(el => {
        el.classList.add('animate-element');
        observer.observe(el);
    });

    // Removed parallax effect to avoid interfering with carousel auto-scroll
}

// Smooth scroll for internal links
function initSmoothScroll() {
    const links = document.querySelectorAll('a[href^="#"]');

    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 120;

                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Scroll effects and animations
function initScrollEffects() {
    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.section-header, .feature, .collection-card, .gallery-item, .contact-item');
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
    
    // Parallax effect for hero section
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const heroBackground = document.querySelector('.hero-bg');
        if (heroBackground) {
            heroBackground.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });
}

// Contact form functionality
function initContactForm() {
    const contactForm = document.querySelector('.contact-form form');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const name = this.querySelector('input[type="text"]').value;
            const email = this.querySelector('input[type="email"]').value;
            const message = this.querySelector('textarea').value;
            
            // Basic validation
            if (!name || !email || !message) {
                showNotification('Please fill in all fields.', 'error');
                return;
            }
            
            if (!isValidEmail(email)) {
                showNotification('Please enter a valid email address.', 'error');
                return;
            }
            
            // Simulate form submission
            showNotification('Thank you for your message! We will get back to you soon.', 'success');
            this.reset();
        });
    }
}

// Gallery functionality
function initGallery() {
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    galleryItems.forEach(item => {
        item.addEventListener('click', function() {
            const img = this.querySelector('img');
            if (img) {
                openLightbox(img.src, img.alt);
            }
        });
    });
}

// Mobile menu functionality
function initMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileToggle && navMenu) {
        mobileToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            this.classList.toggle('active');
        });
        
        // Close menu when clicking on a link
        const navLinks = navMenu.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                navMenu.classList.remove('active');
                mobileToggle.classList.remove('active');
            });
        });
    }
}

// Utility functions
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 15px 20px;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        max-width: 400px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Close functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', function() {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    });
    
    // Auto close after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }
    }, 5000);
}

function openLightbox(src, alt) {
    // Create lightbox
    const lightbox = document.createElement('div');
    lightbox.className = 'lightbox';
    lightbox.innerHTML = `
        <div class="lightbox-content">
            <img src="${src}" alt="${alt}">
            <button class="lightbox-close">&times;</button>
        </div>
    `;
    
    // Add styles
    lightbox.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    
    const content = lightbox.querySelector('.lightbox-content');
    content.style.cssText = `
        position: relative;
        max-width: 90%;
        max-height: 90%;
    `;
    
    const img = lightbox.querySelector('img');
    img.style.cssText = `
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    `;
    
    const closeBtn = lightbox.querySelector('.lightbox-close');
    closeBtn.style.cssText = `
        position: absolute;
        top: -40px;
        right: 0;
        background: none;
        border: none;
        color: white;
        font-size: 30px;
        cursor: pointer;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    `;
    
    document.body.appendChild(lightbox);
    
    // Animate in
    setTimeout(() => {
        lightbox.style.opacity = '1';
    }, 100);
    
    // Close functionality
    closeBtn.addEventListener('click', closeLightbox);
    lightbox.addEventListener('click', function(e) {
        if (e.target === lightbox) {
            closeLightbox();
        }
    });
    
    // Close on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeLightbox();
        }
    });
    
    function closeLightbox() {
        lightbox.style.opacity = '0';
        setTimeout(() => {
            if (document.body.contains(lightbox)) {
                document.body.removeChild(lightbox);
            }
        }, 300);
    }
}

// Smooth scroll polyfill for older browsers
if (!('scrollBehavior' in document.documentElement.style)) {
    const smoothScrollPolyfill = function(target, duration = 1000) {
        const targetPosition = target.offsetTop - 120;
        const startPosition = window.pageYOffset;
        const distance = targetPosition - startPosition;
        let startTime = null;
        
        function animation(currentTime) {
            if (startTime === null) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const run = ease(timeElapsed, startPosition, distance, duration);
            window.scrollTo(0, run);
            if (timeElapsed < duration) requestAnimationFrame(animation);
        }
        
        function ease(t, b, c, d) {
            t /= d / 2;
            if (t < 1) return c / 2 * t * t + b;
            t--;
            return -c / 2 * (t * (t - 2) - 1) + b;
        }
        
        requestAnimationFrame(animation);
    };
    
    // Override smooth scroll for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                smoothScrollPolyfill(target);
            }
        });
    });
}

// Featured Carousel Functionality
function initFeaturedCarousel() {
    const track = document.getElementById('featuredCarouselTrack');
    const prevBtn = document.getElementById('featuredPrev');
    const nextBtn = document.getElementById('featuredNext');
    const dotsContainer = document.getElementById('featuredDots');

    if (!track || !prevBtn || !nextBtn || !dotsContainer) return;

    const items = track.querySelectorAll('.featured-item');
    const itemsPerView = 3;
    const totalSlides = Math.max(1, items.length - itemsPerView + 1);
    let currentSlide = 0;

    // Create dots
    for (let i = 0; i < totalSlides; i++) {
        const dot = document.createElement('div');
        dot.className = 'carousel-dot';
        if (i === 0) dot.classList.add('active');
        dot.addEventListener('click', () => goToSlide(i));
        dotsContainer.appendChild(dot);
    }

    const dots = dotsContainer.querySelectorAll('.carousel-dot');

    function updateCarousel() {
        const itemWidth = 100 / itemsPerView; // Each item takes 33.33% width
        const translateX = -(currentSlide * itemWidth);
        track.style.transform = `translateX(${translateX}%)`;

        // Update dots
        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === currentSlide);
        });

        // Update button states
        prevBtn.style.opacity = currentSlide === 0 ? '0.5' : '1';
        nextBtn.style.opacity = currentSlide === totalSlides - 1 ? '0.5' : '1';
    }

    function goToSlide(slideIndex) {
        currentSlide = Math.max(0, Math.min(slideIndex, totalSlides - 1));
        updateCarousel();
    }

    function nextSlide() {
        if (currentSlide < totalSlides - 1) {
            currentSlide++;
        } else {
            currentSlide = 0; // Loop back to start
        }
        updateCarousel();
    }

    function prevSlide() {
        if (currentSlide > 0) {
            currentSlide--;
            updateCarousel();
        }
    }

    // Event listeners
    nextBtn.addEventListener('click', nextSlide);
    prevBtn.addEventListener('click', prevSlide);

    // Auto-scroll functionality
    let autoScrollInterval = setInterval(nextSlide, 5000);

    // Pause auto-scroll on hover
    track.addEventListener('mouseenter', () => {
        clearInterval(autoScrollInterval);
    });

    track.addEventListener('mouseleave', () => {
        autoScrollInterval = setInterval(nextSlide, 5000);
    });

    // Initialize
    updateCarousel();

    // Handle window resize
    window.addEventListener('resize', () => {
        updateCarousel();
    });
}
