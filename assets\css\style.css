/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #1a1a1a;
    --secondary-color: #6b6b6b;
    --accent-color: #c9a96e;
    --accent-light: #f4e4bc;
    --dark-color: #0a0a0a;
    --light-color: #fafafa;
    --white: #ffffff;
    --black: #000000;
    --gray-light: #f8f9fa;
    --gray-medium: #e9ecef;
    --gray-dark: #6c757d;
    --border-color: #dee2e6;
    --text-muted: #8e8e93;

    --font-primary: 'Cormorant Garamond', serif;
    --font-secondary: 'Inter', sans-serif;
    --font-accent: 'Space Grotesk', sans-serif;

    --transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    --transition-fast: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    --shadow-hover: 0 16px 48px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 24px 64px rgba(0, 0, 0, 0.12);
    --shadow-elegant: 0 4px 24px rgba(201, 169, 110, 0.15);

    --border-radius: 2px;
    --border-radius-lg: 8px;
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 2rem;
    --spacing-lg: 4rem;
    --spacing-xl: 6rem;
    --spacing-xxl: 8rem;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.7;
    color: var(--primary-color);
    background-color: var(--white);
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.img-fluid {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 400;
    line-height: 1.3;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 300;
    letter-spacing: -0.02em;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 400;
    letter-spacing: -0.01em;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 400;
}

h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.5rem);
    font-weight: 500;
}

p {
    margin-bottom: var(--spacing-sm);
    color: var(--secondary-color);
    font-size: 1rem;
    line-height: 1.7;
}

.lead {
    font-size: clamp(1.125rem, 2vw, 1.25rem);
    font-weight: 400;
    color: var(--primary-color);
    line-height: 1.6;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 2rem;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    font-family: var(--font-accent);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    letter-spacing: 0.025em;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    min-height: 48px;
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--dark-color);
    border-color: var(--dark-color);
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--gray-light);
    border-color: var(--primary-color);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-outline-light {
    background-color: transparent;
    color: var(--white);
    border-color: rgba(255, 255, 255, 0.3);
}

.btn-outline-light:hover {
    background-color: var(--white);
    color: var(--primary-color);
    border-color: var(--white);
}

/* Section Styles */
.section-header {
    margin-bottom: var(--spacing-xl);
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-subtitle {
    display: inline-block;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.2em;
    color: var(--accent-color);
    margin-bottom: var(--spacing-md);
    font-family: var(--font-accent);
    position: relative;
    padding: 0 var(--spacing-md);
}

.section-subtitle::before,
.section-subtitle::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 40px;
    height: 1px;
    background: var(--accent-color);
}

.section-subtitle::before {
    left: -50px;
}

.section-subtitle::after {
    right: -50px;
}

.section-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-weight: 300;
    line-height: 1.2;
    letter-spacing: -0.02em;
}

.section-description {
    font-size: 1.125rem;
    color: var(--secondary-color);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.7;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
}

.header-top {
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-xs) 0;
    font-size: 0.75rem;
}

.header-top-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info span {
    margin-right: var(--spacing-md);
    font-family: var(--font-accent);
    font-weight: 400;
}

.contact-info i {
    margin-right: var(--spacing-xs);
    color: var(--accent-color);
}

.social-links a {
    color: var(--white);
    margin-left: var(--spacing-sm);
    transition: var(--transition);
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.social-links a:hover {
    color: var(--accent-color);
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar {
    padding: var(--spacing-sm) 0;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
}

.logo-img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    margin-right: var(--spacing-sm);
}

.logo-text h1 {
    font-size: 1.25rem;
    margin-bottom: 0;
    color: var(--primary-color);
    font-weight: 400;
    letter-spacing: -0.01em;
}

.logo-text span {
    font-size: 0.625rem;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-family: var(--font-accent);
    font-weight: 500;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
}

.nav-menu li {
    margin: 0 var(--spacing-md);
    position: relative;
}

.nav-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
    transition: var(--transition);
    display: flex;
    align-items: center;
    font-family: var(--font-accent);
    padding: var(--spacing-xs) 0;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--accent-color);
    transition: var(--transition);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link i {
    margin-left: var(--spacing-xs);
    font-size: 0.625rem;
    transition: var(--transition);
}

.dropdown:hover .nav-link i {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + 10px);
    left: 0;
    background-color: var(--white);
    box-shadow: var(--shadow-large);
    list-style: none;
    padding: var(--spacing-sm) 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    display: block;
    padding: var(--spacing-xs) var(--spacing-sm);
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.875rem;
    font-family: var(--font-accent);
}

.dropdown-menu a:hover {
    background-color: var(--gray-light);
    color: var(--primary-color);
}

.nav-actions {
    display: flex;
    align-items: center;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    margin-left: 20px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--dark-color);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    margin-top: 120px;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-carousel-left,
.hero-carousel-right {
    position: absolute;
    top: 0;
    width: 280px;
    height: 100%;
    overflow: hidden;
    opacity: 0.4;
    transition: var(--transition);
}

.hero-carousel-left {
    left: 10%;
}

.hero-carousel-right {
    right: 10%;
}

.hero-carousel-left::before,
.hero-carousel-right::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.02) 20%,
        rgba(255, 255, 255, 0) 50%,
        rgba(255, 255, 255, 0.02) 80%,
        rgba(255, 255, 255, 0.1) 100%
    );
    pointer-events: none;
    z-index: 2;
}

.carousel-track {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    will-change: transform;
    animation-fill-mode: both;
}

/* Animation will be applied via JavaScript for better control */

@keyframes scrollUp {
    0% {
        transform: translateY(100%);
    }
    100% {
        transform: translateY(-100%);
    }
}

@keyframes scrollDown {
    0% {
        transform: translateY(-100%);
    }
    100% {
        transform: translateY(100%);
    }
}

.carousel-item {
    width: 220px;
    height: 280px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    transition: var(--transition);
    margin: 0 auto;
}

.carousel-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: var(--transition);
}

.carousel-item:hover {
    transform: scale(1.05);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.25);
    z-index: 10;
    position: relative;
}

.hero-carousel-left:hover,
.hero-carousel-right:hover {
    opacity: 0.6;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.hero-label {
    display: inline-block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 0.2em;
    margin-bottom: var(--spacing-sm);
    font-family: var(--font-accent);
}

.hero-title {
    font-size: clamp(3.5rem, 8vw, 7rem);
    font-weight: 300;
    line-height: 0.9;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    letter-spacing: -0.03em;
    font-family: var(--font-primary);
}

.hero-subtitle {
    font-size: clamp(1.25rem, 3vw, 1.75rem);
    font-weight: 400;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-lg);
    font-family: var(--font-primary);
    font-style: italic;
    line-height: 1.4;
}

.hero-description {
    font-size: 1.125rem;
    line-height: 1.8;
    margin-bottom: var(--spacing-xl);
    color: var(--secondary-color);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.hero-actions .btn {
    padding: 1.25rem 2.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 0.05em;
    border-radius: 0;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.hero-actions .btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    border: none;
    color: var(--white);
}

.hero-actions .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.hero-actions .btn-primary i {
    margin-left: var(--spacing-xs);
    transition: var(--transition);
}

.hero-actions .btn-primary:hover i {
    transform: translateX(4px);
}

.hero-actions .btn-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.hero-actions .btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    max-width: 500px;
    margin: 0 auto;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 300;
    color: var(--primary-color);
    font-family: var(--font-primary);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--secondary-color);
    font-weight: 500;
    font-family: var(--font-accent);
}

.hero-scroll-indicator {
    position: absolute;
    bottom: calc(var(--spacing-lg) + 60px);
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: var(--secondary-color);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-family: var(--font-accent);
    z-index: 2;
}

.scroll-line {
    width: 1px;
    height: 40px;
    background: linear-gradient(to bottom, var(--secondary-color), transparent);
    margin: var(--spacing-sm) auto 0;
    animation: scrollPulse 2s ease-in-out infinite;
}

@keyframes scrollPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scaleY(1);
    }
    50% {
        opacity: 1;
        transform: scaleY(1.2);
    }
}

/* Museum Stats */
.museum-stats {
    padding: var(--spacing-xl) 0;
    background-color: var(--gray-light);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
}

.stat-number {
    font-size: clamp(2.5rem, 4vw, 3.5rem);
    font-weight: 300;
    color: var(--primary-color);
    font-family: var(--font-primary);
    margin-bottom: var(--spacing-xs);
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--secondary-color);
    font-weight: 500;
    font-family: var(--font-accent);
}

/* About Section */
.about {
    padding: var(--spacing-xl) 0;
    background-color: var(--white);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-features {
    margin-top: 40px;
}

.feature {
    display: flex;
    margin-bottom: 30px;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.feature-icon i {
    font-size: 24px;
}

.feature-content h3 {
    margin-bottom: 10px;
    color: var(--dark-color);
}

.about-image {
    position: relative;
}

.about-image img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    box-shadow: var(--shadow);
}

/* Collections Section */
.collections {
    padding: var(--spacing-xxl) 0;
    background: linear-gradient(135deg, var(--white) 0%, var(--gray-light) 100%);
}

.collections-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xxl);
    margin-top: var(--spacing-xl);
}

.collection-card {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: var(--transition);
    box-shadow: var(--shadow);
    position: relative;
}

.collection-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--accent-light));
    transform: scaleX(0);
    transition: var(--transition);
}

.collection-card:hover::before {
    transform: scaleX(1);
}

.collection-card:hover {
    box-shadow: var(--shadow-elegant);
    transform: translateY(-8px);
}

.collection-content {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl);
    align-items: center;
}

.collection-image {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    background: var(--gray-light);
    padding: var(--spacing-md);
}

.collection-image img {
    width: 100%;
    height: auto;
    max-height: 350px;
    object-fit: contain;
    object-position: center;
    transition: var(--transition);
    border-radius: var(--border-radius);
}

.collection-card:hover .collection-image img {
    transform: scale(1.02);
}

.collection-info {
    position: relative;
    padding: var(--spacing-md) 0;
}

.collection-number {
    font-size: 8rem;
    font-weight: 100;
    color: var(--accent-light);
    font-family: var(--font-primary);
    position: absolute;
    top: -4rem;
    left: -2rem;
    z-index: 1;
    line-height: 1;
    user-select: none;
}

.collection-info h3 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
    position: relative;
    z-index: 2;
    color: var(--primary-color);
    font-weight: 400;
}

.collection-info p {
    margin-bottom: var(--spacing-lg);
    line-height: 1.8;
    font-size: 1.1rem;
    color: var(--secondary-color);
}

.collection-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

.item-count {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
    font-family: var(--font-accent);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.collection-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-family: var(--font-accent);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    transition: var(--transition);
    position: relative;
    padding-right: 1.5rem;
}

.collection-link::after {
    content: '→';
    position: absolute;
    right: 0;
    top: 0;
    transition: var(--transition);
}

.collection-link:hover {
    color: var(--accent-color);
}

.collection-link:hover::after {
    transform: translateX(4px);
}

/* Featured Items */
.featured-items {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

/* Featured Carousel */
.featured-carousel-container {
    position: relative;
    margin-top: var(--spacing-xl);
}

.featured-carousel {
    overflow: hidden;
    border-radius: 12px;
}

.featured-carousel-track {
    display: flex;
    transition: transform 0.5s ease;
    gap: var(--spacing-md);
}

.featured-carousel-track .featured-item {
    flex: 0 0 calc(33.333% - var(--spacing-md));
    min-width: 350px;
}

.featured-carousel-controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

.carousel-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
    background: var(--white);
    color: var(--primary-color);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.carousel-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

.featured-carousel-dots {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-md);
}

.carousel-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--gray-medium);
    cursor: pointer;
    transition: var(--transition);
}

.carousel-dot.active {
    background: var(--primary-color);
    transform: scale(1.2);
}

/* Responsive Design for Featured Carousel */
@media (max-width: 1024px) {
    .featured-carousel-track .featured-item {
        flex: 0 0 calc(50% - var(--spacing-md));
        min-width: 300px;
    }
}

@media (max-width: 768px) {
    .featured-carousel-track .featured-item {
        flex: 0 0 100%;
        min-width: 280px;
    }

    .carousel-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

.featured-item {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: var(--transition);
    box-shadow: var(--shadow);
    position: relative;
    group: hover;
}

.featured-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(201, 169, 110, 0.05));
    opacity: 0;
    transition: var(--transition);
    z-index: 1;
}

.featured-item:hover::before {
    opacity: 1;
}

.featured-item:hover {
    box-shadow: var(--shadow-elegant);
    transform: translateY(-12px);
}

.featured-image {
    position: relative;
    overflow: hidden;
    height: 300px;
    background: var(--gray-light);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
}

.featured-image img {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    object-position: center;
    transition: var(--transition);
    border-radius: var(--border-radius);
}

.featured-item:hover .featured-image img {
    transform: scale(1.05);
}

.featured-content {
    padding: var(--spacing-lg);
    position: relative;
    z-index: 2;
}

.featured-category {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.15em;
    color: var(--accent-color);
    font-weight: 600;
    font-family: var(--font-accent);
    margin-bottom: var(--spacing-sm);
    display: block;
}

.featured-content h3 {
    font-size: 1.75rem;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    font-weight: 400;
    line-height: 1.3;
}

.featured-content p {
    margin-bottom: var(--spacing-lg);
    line-height: 1.7;
    font-size: 1rem;
    color: var(--secondary-color);
}

.featured-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-family: var(--font-accent);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    position: relative;
}

.featured-link::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: var(--transition);
}

.featured-link:hover::before {
    width: 100%;
}

.featured-link:hover {
    color: var(--accent-color);
}

.featured-link::after {
    content: '→';
    transition: var(--transition);
    font-size: 1rem;
}

.featured-link:hover::after {
    transform: translateX(4px);
}

/* Item Showcase */
.item-showcase {
    padding: var(--spacing-xxl) 0;
    background: linear-gradient(135deg, var(--gray-light) 0%, var(--white) 100%);
}

.showcase-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.showcase-item {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: var(--transition);
    box-shadow: var(--shadow);
    position: relative;
    cursor: pointer;
    transform: translateY(0) scale(1);
}

.showcase-item:hover {
    box-shadow: var(--shadow-elegant);
    transform: translateY(-8px) scale(1.02);
}



.showcase-image {
    position: relative;
    overflow: hidden;
    height: 250px;
    background: var(--gray-light);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
}

.showcase-image img {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    object-position: center;
    transition: var(--transition);
    border-radius: var(--border-radius);
}

.showcase-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        to bottom,
        transparent 0%,
        transparent 40%,
        rgba(0, 0, 0, 0.7) 100%
    );
    display: flex;
    align-items: flex-end;
    padding: var(--spacing-md);
    opacity: 0;
    transition: var(--transition);
    border-radius: var(--border-radius);
}

.showcase-overlay-content {
    color: var(--white);
    text-align: left;
    width: 100%;
}

.showcase-overlay-category {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--accent-color);
    font-weight: 600;
    font-family: var(--font-accent);
    display: block;
    margin-bottom: var(--spacing-xs);
}

.showcase-overlay h4 {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-xs);
    color: var(--white);
    font-weight: 400;
    line-height: 1.3;
}

.showcase-overlay p {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0;
    font-family: var(--font-accent);
}

.showcase-item:hover .showcase-image img {
    transform: scale(1.05);
}

.showcase-item:hover .showcase-overlay {
    opacity: 1;
}

.showcase-content {
    padding: var(--spacing-md);
}

.showcase-category {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.15em;
    color: var(--accent-color);
    font-weight: 600;
    font-family: var(--font-accent);
    margin-bottom: var(--spacing-xs);
    display: block;
}

.showcase-content h3 {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-xs);
    color: var(--primary-color);
    font-weight: 400;
    line-height: 1.3;
}

.showcase-meta {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-sm);
    font-family: var(--font-accent);
}

.showcase-description {
    font-size: 0.9rem;
    color: var(--secondary-color);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.showcase-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-family: var(--font-accent);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.showcase-link:hover {
    color: var(--accent-color);
}

.showcase-link::after {
    content: '→';
    transition: var(--transition);
}

.showcase-link:hover::after {
    transform: translateX(4px);
}

.showcase-actions {
    text-align: center;
    margin-top: var(--spacing-xl);
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

/* Gallery Preview */
/* Collection Styles */
.collection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.collection-item {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: var(--transition);
    box-shadow: var(--shadow);
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    cursor: pointer;
}

.collection-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-elegant);
}

.collection-item .item-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.collection-item .item-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    transition: var(--transition);
    background-color: var(--light-gray);
}

.collection-item .item-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        to bottom,
        transparent 0%,
        transparent 60%,
        rgba(0, 0, 0, 0.8) 100%
    );
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding: var(--spacing-md);
    opacity: 0;
    transition: var(--transition);
}

.collection-item:hover .item-overlay {
    opacity: 1;
}

.collection-item .view-btn {
    background-color: var(--accent-color);
    color: var(--white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.collection-item .view-btn:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
}

.collection-item .item-info {
    padding: var(--spacing-md);
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.collection-item .item-info h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: var(--spacing-xs);
    line-height: 1.3;
}

.collection-item .item-info p {
    color: var(--text-light);
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: var(--spacing-sm);
    flex: 1;
}

.collection-item .item-period {
    font-size: 0.75rem;
    color: var(--accent-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-family: var(--font-accent);
}

.gallery-preview {
    padding: var(--spacing-xl) 0;
    background-color: var(--white);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    height: 300px;
    cursor: pointer;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-item:hover img {
    transform: scale(1.1);
}

/* Contact Section */
.contact {
    padding: 100px 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-info h2 {
    margin-bottom: 40px;
    color: var(--dark-color);
}

.contact-item {
    display: flex;
    margin-bottom: 30px;
}

.contact-item i {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.contact-item h3 {
    margin-bottom: 10px;
    color: var(--dark-color);
}

.contact-form {
    background-color: var(--gray-light);
    padding: 40px;
}

.contact-form h3 {
    margin-bottom: 30px;
    color: var(--dark-color);
}

.form-group {
    margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 1px solid var(--gray-medium);
    background-color: var(--white);
    font-family: var(--font-secondary);
    font-size: 14px;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* ===== LEGAL PAGES STYLES ===== */
.legal-content {
    padding: var(--spacing-xl) 0;
    background: var(--light-bg);
}

.legal-wrapper {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.legal-text {
    line-height: 1.8;
}

.legal-text h2 {
    color: var(--primary-color);
    font-family: var(--font-heading);
    font-size: 1.75rem;
    margin: var(--spacing-lg) 0 var(--spacing-md) 0;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--accent-color);
}

.legal-text h3 {
    color: var(--secondary-color);
    font-family: var(--font-heading);
    font-size: 1.25rem;
    margin: var(--spacing-md) 0 var(--spacing-sm) 0;
}

.legal-text p {
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
}

.legal-text ul {
    margin: var(--spacing-sm) 0 var(--spacing-md) var(--spacing-md);
    padding-left: var(--spacing-md);
}

.legal-text li {
    margin-bottom: var(--spacing-xs);
    color: var(--text-color);
}

.last-updated {
    font-style: italic;
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-lg) !important;
    padding: var(--spacing-sm);
    background: var(--light-bg);
    border-radius: var(--border-radius-sm);
}

.contact-info {
    background: var(--light-bg);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    margin-top: var(--spacing-md);
    border-left: 4px solid var(--accent-color);
}

.contact-info p {
    margin-bottom: var(--spacing-xs) !important;
}

/* ===== FAQ STYLES ===== */
.faq-content {
    padding: var(--spacing-xl) 0;
    background: var(--light-bg);
}

.faq-wrapper {
    max-width: 900px;
    margin: 0 auto;
}

.faq-categories {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xl);
    flex-wrap: wrap;
}

.faq-category-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: white;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    font-size: 0.9rem;
}

.faq-category-btn.active,
.faq-category-btn:hover {
    background: var(--primary-color);
    color: white;
}

.faq-section {
    display: none;
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.faq-section.active {
    display: block;
}

.faq-section h2 {
    color: var(--primary-color);
    font-family: var(--font-heading);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--accent-color);
}

.faq-item {
    border-bottom: 1px solid var(--light-bg);
    margin-bottom: var(--spacing-md);
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    cursor: pointer;
    transition: var(--transition);
}

.faq-question:hover {
    color: var(--primary-color);
}

.faq-question h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--secondary-color);
}

.faq-question i {
    color: var(--primary-color);
    transition: var(--transition);
    font-size: 0.9rem;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    color: var(--text-color);
    line-height: 1.7;
}

.faq-item.active .faq-answer {
    max-height: 500px;
    padding-bottom: var(--spacing-md);
}

.faq-contact {
    text-align: center;
    margin-top: var(--spacing-xxl);
    padding: var(--spacing-xl);
    background: var(--light-bg);
    border-radius: var(--border-radius);
}

.faq-contact h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.faq-contact p {
    color: var(--text-color);
    margin-bottom: var(--spacing-lg);
}

/* ===== CONTACT PAGE BUSINESS INFO ===== */
.business-info-section {
    padding: var(--spacing-xl) 0;
    background: var(--light-bg);
}

.business-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.hours-card,
.services-card,
.heritage-card {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    text-align: center;
    transition: var(--transition);
}

.hours-card:hover,
.services-card:hover,
.heritage-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
}

.card-icon i {
    font-size: 1.5rem;
    color: white;
}

.hours-card h3,
.services-card h3,
.heritage-card h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-family: var(--font-heading);
}

.hours-list {
    text-align: left;
    margin-bottom: var(--spacing-md);
}

.hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--light-bg);
}

.hours-item:last-child {
    border-bottom: none;
}

.day {
    font-weight: 500;
    color: var(--secondary-color);
}

.time {
    color: var(--text-color);
    font-size: 0.9rem;
}

.special-note {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    background: var(--light-bg);
    border-radius: var(--border-radius-sm);
    font-size: 0.85rem;
    color: var(--text-light);
}

.special-note i {
    color: var(--accent-color);
}

.services-list {
    list-style: none;
    padding: 0;
    text-align: left;
}

.services-list li {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    color: var(--text-color);
}

.services-list i {
    color: var(--accent-color);
    margin-right: var(--spacing-sm);
    font-size: 0.9rem;
}

.heritage-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.heritage-stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--secondary-color);
    font-family: var(--font-heading);
}

.stat-label {
    font-size: 0.85rem;
    color: var(--text-light);
    margin-top: var(--spacing-xs);
}

/* ===== WORKSHOP INFO SECTION ===== */
.workshop-info-section {
    padding: var(--spacing-xxl) 0;
    background: white;
}

.workshop-info-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: center;
}

.workshop-text h2 {
    color: var(--primary-color);
    font-family: var(--font-heading);
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
}

.workshop-description {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-color);
    margin-bottom: var(--spacing-xl);
}

.workshop-highlights {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.highlight-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.highlight-item i {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.highlight-item h4 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xs);
    font-family: var(--font-heading);
}

.highlight-item p {
    color: var(--text-color);
    line-height: 1.6;
    margin: 0;
}

.workshop-image {
    position: relative;
}

.workshop-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.image-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.image-caption p {
    margin: 0;
    font-size: 0.9rem;
    font-style: italic;
}

/* ===== FOOTER STYLES ===== */
.footer {
    background: linear-gradient(135deg, var(--dark-color) 0%, #1a1a1a 100%);
    color: var(--white);
    padding: var(--spacing-xxl) 0 0;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--secondary-color), transparent);
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr 1.5fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

/* Footer Brand Section */
.footer-brand {
    padding-right: var(--spacing-lg);
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.footer-logo img {
    width: 50px;
    height: 50px;
    margin-right: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.footer-logo h3 {
    color: var(--white);
    margin: 0;
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
    font-size: 0.95rem;
}

/* Footer Sections */
.footer-section h4 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
    font-family: var(--font-heading);
    font-size: 1.1rem;
    font-weight: 600;
    position: relative;
    padding-bottom: var(--spacing-xs);
}

.footer-section h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--accent-color);
}

.footer-section h5 {
    color: var(--white);
    margin: var(--spacing-md) 0 var(--spacing-sm) 0;
    font-size: 0.95rem;
    font-weight: 500;
}

/* Footer Links */
.footer-links-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links-list li {
    margin-bottom: var(--spacing-sm);
}

.footer-links-list a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

.footer-links-list a i {
    margin-right: var(--spacing-xs);
    width: 16px;
    color: var(--secondary-color);
    font-size: 0.85rem;
}

.footer-links-list a:hover {
    color: var(--secondary-color);
    transform: translateX(3px);
}

/* Contact Info */
.footer-contact {
    margin-bottom: var(--spacing-lg);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.contact-item i {
    color: var(--secondary-color);
    margin-right: var(--spacing-sm);
    margin-top: 2px;
    width: 16px;
    font-size: 0.9rem;
}

.policies-title {
    margin-top: var(--spacing-lg) !important;
    padding-top: var(--spacing-md);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.policies-links {
    margin-top: var(--spacing-sm);
}

/* Social Media */
.footer-social {
    margin-top: var(--spacing-lg);
}

.social-links {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 42px;
    height: 42px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
    font-size: 1.1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-links a:hover {
    background: var(--secondary-color);
    color: var(--white);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.3);
    border-color: var(--secondary-color);
}

/* Newsletter */
.newsletter-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: var(--spacing-md);
}

.newsletter-form {
    margin-bottom: var(--spacing-lg);
}

.newsletter-input {
    display: flex;
    margin-bottom: var(--spacing-xs);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.newsletter-input input {
    flex: 1;
    padding: var(--spacing-md);
    border: none;
    background: rgba(255, 255, 255, 0.95);
    color: var(--dark-color);
    font-size: 0.9rem;
    outline: none;
}

.newsletter-input input::placeholder {
    color: rgba(0, 0, 0, 0.5);
}

.newsletter-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--secondary-color);
    border: none;
    color: var(--white);
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
}

.newsletter-btn:hover {
    background: var(--accent-color);
}

.newsletter-note {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
    font-style: italic;
}

/* Certifications */
.footer-certifications {
    margin-top: var(--spacing-lg);
    padding-top: var(--spacing-md);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.certification-badge {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.85rem;
}

.certification-badge i {
    color: var(--accent-color);
    margin-right: var(--spacing-xs);
    font-size: 0.9rem;
}

/* Footer Bottom */
.footer-bottom {
    background: rgba(0, 0, 0, 0.3);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--spacing-lg) 0;
    margin-top: var(--spacing-xl);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-copyright p {
    margin: 0;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.footer-tagline {
    color: var(--secondary-color) !important;
    font-style: italic;
    font-size: 0.85rem !important;
    margin-top: var(--spacing-xs) !important;
}

.footer-bottom-links {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.footer-bottom-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.85rem;
    transition: var(--transition);
}

.footer-bottom-links a:hover {
    color: var(--secondary-color);
}

.footer-bottom-links .separator {
    color: rgba(255, 255, 255, 0.3);
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .hero-carousel-left,
    .hero-carousel-right {
        width: 200px;
        opacity: 0.3;
    }

    .hero-carousel-left {
        left: 5%;
    }

    .hero-carousel-right {
        right: 5%;
    }

    .carousel-item {
        width: 160px;
        height: 220px;
    }

    .collection-content {
        grid-template-columns: 300px 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }

    .featured-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: var(--spacing-md);
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .hero {
        margin-top: 100px;
        min-height: 90vh;
    }

    .hero-carousel-left,
    .hero-carousel-right {
        width: 140px;
        opacity: 0.25;
    }

    .hero-carousel-left {
        left: 2%;
    }

    .hero-carousel-right {
        right: 2%;
    }

    .carousel-item {
        width: 110px;
        height: 150px;
    }

    .hero-title {
        font-size: clamp(2.5rem, 8vw, 4rem);
    }

    .hero-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-md);
        max-width: 400px;
    }

    .hero-scroll-indicator {
        bottom: calc(var(--spacing-lg) + 70px);
    }

    .stat-number {
        font-size: 2rem;
    }

    .collection-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        text-align: center;
    }

    .collection-image {
        max-width: 250px;
        margin: 0 auto;
    }

    .collection-number {
        font-size: 6rem;
        top: -3rem;
        left: 50%;
        transform: translateX(-50%);
    }

    .featured-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .featured-image {
        height: 250px;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .hero-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: center;
    }

    .hero-actions .btn {
        min-width: 250px;
        justify-content: center;
    }

    .section-subtitle::before,
    .section-subtitle::after {
        width: 20px;
    }

    .section-subtitle::before {
        left: -30px;
    }

    .section-subtitle::after {
        right: -30px;
    }
}

@media (max-width: 480px) {
    .hero-carousel-left,
    .hero-carousel-right {
        display: none;
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
        max-width: 200px;
    }

    .hero-scroll-indicator {
        bottom: calc(var(--spacing-lg) + 80px);
    }

    .stat-number {
        font-size: 1.75rem;
    }

    .collection-number {
        font-size: 4rem;
        top: -2rem;
    }

    .featured-grid {
        grid-template-columns: 1fr;
    }

    .featured-image {
        height: 200px;
        padding: var(--spacing-sm);
    }

    .featured-content {
        padding: var(--spacing-md);
    }

    .section-subtitle {
        padding: 0;
    }

    .section-subtitle::before,
    .section-subtitle::after {
        display: none;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .footer-brand {
        padding-right: 0;
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .footer-bottom-links {
        justify-content: center;
    }

    .newsletter-input {
        flex-direction: column;
    }

    .newsletter-btn {
        border-radius: var(--border-radius-sm);
    }

    .social-links {
        justify-content: center;
    }

    .faq-categories {
        flex-direction: column;
        align-items: center;
    }

    .faq-category-btn {
        width: 200px;
        text-align: center;
    }

    .faq-section {
        padding: var(--spacing-lg);
    }

    .business-info-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .heritage-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .hours-card,
    .services-card,
    .heritage-card {
        padding: var(--spacing-lg);
    }

    .workshop-info-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .workshop-text h2 {
        font-size: 2rem;
    }

    .workshop-image {
        order: -1;
    }

    .workshop-image img {
        height: 300px;
    }
}

/* Founder Section */
.founder-section {
    padding: var(--spacing-xxl) 0;
    background: var(--white);
}

.founder-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-xxl);
    align-items: start;
    margin-top: var(--spacing-xl);
}

.founder-story p {
    margin-bottom: var(--spacing-lg);
    line-height: 1.8;
    color: var(--text-color);
    font-size: 1.1rem;
}

.founder-images {
    display: grid;
    gap: var(--spacing-lg);
}

.founder-image {
    position: relative;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.founder-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: var(--transition);
}

.founder-image:hover img {
    transform: scale(1.05);
}

.founder-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--white);
    padding: var(--spacing-lg);
    text-align: center;
}

.founder-caption h4 {
    margin-bottom: var(--spacing-xs);
    font-size: 1.2rem;
}

.founder-caption p {
    margin: 0;
    opacity: 0.9;
}

/* Founder Section - Homepage */
.founder-section-home {
    padding: var(--spacing-xxl) 0;
    background: var(--gray-light);
}

.founder-content-home {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: center;
    margin-top: var(--spacing-xl);
}

.founder-text p {
    margin-bottom: var(--spacing-lg);
    line-height: 1.8;
    color: var(--text-color);
    font-size: 1.1rem;
}

.founder-cta {
    margin-top: var(--spacing-xl);
}

.founder-images-home {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.founder-image-home {
    position: relative;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    aspect-ratio: 4/5;
}

.founder-image-home img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.founder-image-home:hover img {
    transform: scale(1.05);
}

.founder-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--white);
    padding: var(--spacing-md);
    text-align: center;
}

.founder-overlay h4 {
    margin-bottom: var(--spacing-xs);
    font-size: 1rem;
}

.founder-overlay p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

/* Responsive Design for Founder Sections */
@media (max-width: 768px) {
    .founder-content,
    .founder-content-home {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .founder-images-home {
        grid-template-columns: 1fr;
    }

    .founder-image-home {
        aspect-ratio: 3/4;
    }
}
