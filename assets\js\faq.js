// FAQ Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initFAQCategories();
    initFAQAccordion();
    initFAQAnimations();
});

// FAQ Category Switching
function initFAQCategories() {
    const categoryBtns = document.querySelectorAll('.faq-category-btn');
    const faqSections = document.querySelectorAll('.faq-section');
    
    categoryBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            
            // Update active button
            categoryBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Show corresponding section
            faqSections.forEach(section => {
                section.classList.remove('active');
                if (section.id === category) {
                    section.classList.add('active');
                    
                    // Animate section appearance
                    section.style.opacity = '0';
                    section.style.transform = 'translateY(20px)';
                    
                    setTimeout(() => {
                        section.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    }, 50);
                }
            });
            
            // Scroll to FAQ section
            document.querySelector('.faq-content').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        });
    });
}

// FAQ Accordion Functionality
function initFAQAccordion() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');
        
        question.addEventListener('click', function() {
            const isActive = item.classList.contains('active');
            
            // Close all other FAQ items in the same section
            const currentSection = item.closest('.faq-section');
            const sectionItems = currentSection.querySelectorAll('.faq-item');
            
            sectionItems.forEach(sectionItem => {
                if (sectionItem !== item) {
                    sectionItem.classList.remove('active');
                    const sectionAnswer = sectionItem.querySelector('.faq-answer');
                    sectionAnswer.style.maxHeight = '0';
                }
            });
            
            // Toggle current item
            if (isActive) {
                item.classList.remove('active');
                answer.style.maxHeight = '0';
            } else {
                item.classList.add('active');
                answer.style.maxHeight = answer.scrollHeight + 'px';
                
                // Scroll to question if needed
                setTimeout(() => {
                    const rect = question.getBoundingClientRect();
                    if (rect.top < 100) {
                        question.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }, 300);
            }
        });
    });
}

// FAQ Animations on Scroll
function initFAQAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe FAQ items
    const faqItems = document.querySelectorAll('.faq-item');
    faqItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        item.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
        observer.observe(item);
    });
    
    // Observe category buttons
    const categoryBtns = document.querySelectorAll('.faq-category-btn');
    categoryBtns.forEach((btn, index) => {
        btn.style.opacity = '0';
        btn.style.transform = 'translateY(20px)';
        btn.style.transition = `opacity 0.5s ease ${index * 0.1}s, transform 0.5s ease ${index * 0.1}s`;
        observer.observe(btn);
    });
}

// Search functionality (if search box is added)
function initFAQSearch() {
    const searchInput = document.querySelector('.faq-search input');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const faqItems = document.querySelectorAll('.faq-item');
            const faqSections = document.querySelectorAll('.faq-section');
            
            if (searchTerm === '') {
                // Show all items and restore category functionality
                faqItems.forEach(item => {
                    item.style.display = 'block';
                });
                
                // Show only active section
                faqSections.forEach(section => {
                    if (section.classList.contains('active')) {
                        section.style.display = 'block';
                    } else {
                        section.style.display = 'none';
                    }
                });
            } else {
                // Show all sections when searching
                faqSections.forEach(section => {
                    section.style.display = 'block';
                });
                
                // Filter items based on search
                faqItems.forEach(item => {
                    const question = item.querySelector('.faq-question h3').textContent.toLowerCase();
                    const answer = item.querySelector('.faq-answer').textContent.toLowerCase();
                    
                    if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                        item.style.display = 'block';
                        
                        // Highlight search terms
                        highlightSearchTerm(item, searchTerm);
                    } else {
                        item.style.display = 'none';
                    }
                });
            }
        });
    }
}

// Highlight search terms
function highlightSearchTerm(item, searchTerm) {
    const question = item.querySelector('.faq-question h3');
    const answer = item.querySelector('.faq-answer');
    
    // Remove previous highlights
    question.innerHTML = question.textContent;
    answer.innerHTML = answer.textContent;
    
    if (searchTerm.length > 2) {
        const regex = new RegExp(`(${searchTerm})`, 'gi');
        
        question.innerHTML = question.textContent.replace(regex, '<mark>$1</mark>');
        answer.innerHTML = answer.textContent.replace(regex, '<mark>$1</mark>');
    }
}

// Auto-expand FAQ item based on URL hash
function handleURLHash() {
    const hash = window.location.hash;
    if (hash) {
        const targetItem = document.querySelector(hash);
        if (targetItem && targetItem.classList.contains('faq-item')) {
            // Find the section containing this item
            const section = targetItem.closest('.faq-section');
            if (section) {
                // Switch to the correct category
                const categoryId = section.id;
                const categoryBtn = document.querySelector(`[data-category="${categoryId}"]`);
                if (categoryBtn) {
                    categoryBtn.click();
                }
                
                // Expand the FAQ item
                setTimeout(() => {
                    targetItem.querySelector('.faq-question').click();
                    targetItem.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }, 500);
            }
        }
    }
}

// Initialize URL hash handling
document.addEventListener('DOMContentLoaded', function() {
    handleURLHash();
});

// Handle hash changes
window.addEventListener('hashchange', function() {
    handleURLHash();
});

// Add smooth scrolling for anchor links
document.addEventListener('click', function(e) {
    if (e.target.tagName === 'A' && e.target.getAttribute('href').startsWith('#')) {
        e.preventDefault();
        const targetId = e.target.getAttribute('href');
        const targetElement = document.querySelector(targetId);
        
        if (targetElement) {
            targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    }
});

// Export functions for external use
window.FAQModule = {
    initFAQCategories,
    initFAQAccordion,
    initFAQAnimations,
    initFAQSearch
};
